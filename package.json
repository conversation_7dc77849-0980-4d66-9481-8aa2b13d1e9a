{"name": "theinfini_ai_backend", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/index.js", "start:watch": "nodemon dist/index.js", "dev": "nodemon src/index.ts", "dev:build": "concurrently \"npm run build:watch\" \"npm run start:watch\"", "prod": "npm run build && npm start", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@langchain/anthropic": "^0.3.21", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.11", "@pinecone-database/pinecone": "^6.0.1", "@types/multer": "^1.4.13", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.27", "mammoth": "^1.9.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "pdf-poppler": "^0.2.1", "sequelize": "^6.37.7", "sharp": "^0.34.2", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "private": true}