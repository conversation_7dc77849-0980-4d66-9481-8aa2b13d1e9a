export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface LoginRequest {
  identifier: string; // email or mobile
  password?: string;
  requestOtp?: boolean;
}

export interface OTPVerificationRequest {
  identifier: string;
  otp: string;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  llmModel?: string;
}

export interface SimpleChatRequest {
  message: string;
  sessionId?: string;
  llmModel?: string;
}

export interface RegenerateRequest {
  messageId: string;
  llmModel?: string;
}

export interface ChatResponse {
  response: string;
  sessionId: string;
  messageId: string;
  isGuest: boolean;
  remainingGuestChats?: number;
}

export interface SimpleChatResponse {
  sessionId: string;
  messageId: string;
  isGuest: boolean;
  userId?: string;
}

export interface UserAttributes {
  id: string;
  email?: string;
  mobile?: string;
  password?: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatAttributes {
  id: string;
  userId?: string;
  sessionId: string;
  title?: string;
  isGuest: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessageAttributes {
  id: string;
  chatId: string;
  message: string;
  response: string;
  llmModel: string;
  isUserMessage: boolean;
  parentMessageId?: string;
  version: number;
  isRegenerated: boolean;
  attachmentPath?: string;
  attachmentName?: string;
  attachmentType?: string;
  attachmentSize?: number;
  createdAt: Date;
}

export interface UserOTPAttributes {
  id: string;
  userId: string;
  otp: string;
  expiresAt: Date;
  isUsed: boolean;
  createdAt: Date;
}

export interface GuestSessionAttributes {
  id: string;
  sessionId: string;
  messageCount: number;
  ipAddress: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface JWTPayload {
  userId: string;
  email?: string;
  mobile?: string;
  iat: number;
  exp: number;
}

// User Credit System Types
export interface UserCreditAttributes {
  id: string;
  userId: string;
  credits: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreditTransactionAttributes {
  id: string;
  userId: string;
  amount: number;
  type: 'CREDIT' | 'DEBIT';
  description: string;
  createdAt: Date;
}

// User Profile Types
export interface UserProfileAttributes {
  id: string;
  userId: string;
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
  plan: 'FREE' | 'PREMIUM' | 'ENTERPRISE';
  createdAt: Date;
  updatedAt: Date;
}

export interface LLMConfig {
  model: string;
  temperature?: number;
  maxTokens?: number;
  apiKey?: string;
}

export enum LLMProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

import { Request } from 'express';

export interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}

export interface ProjectAttributes {
  id: string;
  userId: string;
  name: string;
  description: string;
  rules: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatThreadAttributes {
  id: string;
  userId?: string;
  projectId?: string;
  sessionId: string;
  name?: string;
  isGuest: boolean;
  createdAt: Date;
  updatedAt: Date;
}
