import { Sequelize } from 'sequelize';
import logger from './logger';
import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

// User Database Configuration
const userDbConfig = {
  host: process.env.USER_DB_HOST || 'localhost',
  port: parseInt(process.env.USER_DB_PORT || '3306'),
  database: process.env.USER_DB_NAME || 'infini_ai_users',
  username: process.env.USER_DB_USERNAME || 'inf_ai_user',
  password: process.env.USER_DB_PASSWORD || 'inf_ai_user',
};

// Chat Database Configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST || 'localhost',
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

// Common Sequelize options
const commonOptions = {
  dialect: 'mysql' as const,
  logging: (msg: string) => logger.debug(msg),
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

// Create Sequelize instances
export const userDatabase = new Sequelize(
  userDbConfig.database,
  userDbConfig.username,
  userDbConfig.password,
  {
    ...commonOptions,
    host: userDbConfig.host,
    port: userDbConfig.port,
  }
);

export const chatDatabase = new Sequelize(
  chatDbConfig.database,
  chatDbConfig.username,
  chatDbConfig.password,
  {
    ...commonOptions,
    host: chatDbConfig.host,
    port: chatDbConfig.port,
  }
);

// Database connection functions
export class DatabaseManager {
  static async connectUserDatabase(): Promise<void> {
    try {
      await userDatabase.authenticate();
      logger.info('User database connection established successfully');
    } catch (error) {
      logger.error('Unable to connect to user database:', error);
      throw error;
    }
  }

  static async connectChatDatabase(): Promise<void> {
    try {
      await chatDatabase.authenticate();
      logger.info('Chat database connection established successfully');
    } catch (error) {
      logger.error('Unable to connect to chat database:', error);
      throw error;
    }
  }

  static async connectAllDatabases(): Promise<void> {
    await Promise.all([
      this.connectUserDatabase(),
      this.connectChatDatabase(),
    ]);
  }

  static async syncUserDatabase(force: boolean = false): Promise<void> {
    try {
      await userDatabase.sync({ force, alter: true });
      logger.info('User database synchronized successfully');
    } catch (error) {
      logger.error('Error synchronizing user database:', error);
      throw error;
    }
  }

  static async syncChatDatabase(force: boolean = false): Promise<void> {
    try {
      await chatDatabase.sync({ force, alter: true });
      logger.info('Chat database synchronized successfully');
    } catch (error) {
      logger.error('Error synchronizing chat database:', error);
      throw error;
    }
  }

  static async syncAllDatabases(force: boolean = false): Promise<void> {
    await Promise.all([
      this.syncUserDatabase(force),
      this.syncChatDatabase(force),
    ]);
  }

  static async closeAllConnections(): Promise<void> {
    await Promise.all([
      userDatabase.close(),
      chatDatabase.close(),
    ]);
    logger.info('All database connections closed');
  }
}
