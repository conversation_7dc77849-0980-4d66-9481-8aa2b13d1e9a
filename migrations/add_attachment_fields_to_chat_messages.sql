-- Migration to add attachment support fields to chat_messages table
-- This enables storing file attachment information with chat messages

USE infini_ai_user_chat_recs;

-- Add new columns for attachment support
ALTER TABLE chat_messages 
ADD COLUMN attachment_path VARCHAR(500) NULL AFTER is_regenerated,
ADD COLUMN attachment_name VARCHAR(255) NULL AFTER attachment_path,
ADD COLUMN attachment_type VARCHAR(100) NULL AFTER attachment_name,
ADD COLUMN attachment_size INT NULL AFTER attachment_type;

-- Add indexes for the new columns to improve query performance
CREATE INDEX idx_chat_messages_attachment_path ON chat_messages(attachment_path);
CREATE INDEX idx_chat_messages_attachment_type ON chat_messages(attachment_type);

-- Add comment to document the purpose
ALTER TABLE chat_messages COMMENT = 'Chat messages with support for file attachments and regeneration';
